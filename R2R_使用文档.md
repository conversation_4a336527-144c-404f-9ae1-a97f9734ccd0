# R2R (Retrieval to Rich<PERSON>) 详细使用文档

## 目录
1. [项目简介](#项目简介)
2. [系统要求](#系统要求)
3. [安装指南](#安装指南)
4. [快速开始](#快速开始)
5. [配置说明](#配置说明)
6. [核心功能](#核心功能)
7. [API 使用指南](#api-使用指南)
8. [部署方式](#部署方式)
9. [高级功能](#高级功能)
10. [故障排除](#故障排除)

## 项目简介

R2R 是一个先进的 AI 检索系统，支持生产级的检索增强生成 (RAG) 功能。它围绕 RESTful API 构建，提供多模态内容摄取、混合搜索、知识图谱和全面的文档管理功能。

### 主要特性
- 📁 **多模态摄取**: 解析 `.txt`, `.pdf`, `.json`, `.png`, `.mp3` 等多种格式
- 🔍 **混合搜索**: 语义搜索 + 关键词搜索，使用倒数排名融合
- 🔗 **知识图谱**: 自动实体和关系提取
- 🤖 **智能体 RAG**: 集成检索的推理智能体
- 🔐 **用户和访问管理**: 完整的身份验证和集合系统
- 🌐 **深度研究 API**: 多步推理系统，从知识库和互联网获取相关数据

## 系统要求

### 基础要求
- **Python**: 3.10+ (支持到 3.12，不支持 3.13)
- **Docker**: 用于容器化部署
- **内存**: 最少 4GB RAM，推荐 8GB+
- **存储**: 至少 10GB 可用空间

### 支持的操作系统
- Linux (推荐)
- macOS
- Windows (通过 WSL2)

## 安装指南

### 方式一：轻量模式安装 (推荐新手)

```bash
# 安装 R2R
pip install r2r

# 设置 OpenAI API 密钥
export OPENAI_API_KEY=sk-your-api-key-here

# 启动服务
python -m r2r.serve
```

### 方式二：完整模式安装 (生产环境)

```bash
# 克隆项目
git clone https://github.com/SciPhi-AI/R2R.git
cd R2R

# 设置环境变量
export R2R_CONFIG_NAME=full
export OPENAI_API_KEY=sk-your-api-key-here

# 使用 Docker Compose 启动
docker compose -f docker/compose.full.yaml --profile postgres up -d
```

### 方式三：开发环境安装

```bash
# 克隆项目
git clone https://github.com/SciPhi-AI/R2R.git
cd R2R/py

# 安装开发依赖
pip install -e ".[core,dev]"

# 启动开发服务器
uvicorn core.main.app_entry:app --host 0.0.0.0 --port 7272 --reload
```

## 快速开始

### 1. 安装 SDK

```bash
# Python SDK
pip install r2r

# JavaScript SDK
npm install r2r-js
```

### 2. 初始化客户端

**Python:**
```python
from r2r import R2RClient

client = R2RClient(base_url="http://localhost:7272")
```

**JavaScript:**
```javascript
const { r2rClient } = require('r2r-js');
const client = new r2rClient("http://localhost:7272");
```

### 3. 基本操作示例

```python
# 创建测试文档
with open("test.txt", "w") as file:
    file.write("John is a person that works at Google.")

# 上传文档
result = client.documents.create(file_path="test.txt")

# 执行基础搜索
search_results = client.retrieval.search(query="Who is John?")

# 执行 RAG 查询
rag_response = client.retrieval.rag(
    query="Who is John?",
    rag_generation_config={
        "model": "openai/gpt-4o-mini",
        "temperature": 0.0
    }
)

print(rag_response.results[0].completion)
```

## 配置说明

### 环境变量配置

R2R 支持通过环境变量进行配置。主要配置项包括：

#### 基础配置
```bash
# 服务配置
R2R_HOST=0.0.0.0
R2R_PORT=7272
R2R_LOG_LEVEL=INFO
R2R_CONFIG_NAME=full  # 或 light
R2R_PROJECT_NAME=r2r_default

# 数据库配置
R2R_POSTGRES_HOST=localhost
R2R_POSTGRES_PORT=5432
R2R_POSTGRES_USER=postgres
R2R_POSTGRES_PASSWORD=postgres
R2R_POSTGRES_DBNAME=postgres
```

#### API 密钥配置
```bash
# OpenAI
OPENAI_API_KEY=sk-your-key-here
OPENAI_API_BASE=https://api.openai.com/v1

# Anthropic
ANTHROPIC_API_KEY=your-key-here

# Azure
AZURE_API_KEY=your-key-here
AZURE_API_BASE=your-endpoint-here

# 网络搜索
FIRECRAWL_API_KEY=your-key-here
SERPER_API_KEY=your-key-here
TAVILY_API_KEY=your-key-here
```

### 配置文件

R2R 使用 TOML 格式的配置文件。默认配置文件位于 `py/r2r/r2r.toml`：

```toml
[app]
default_max_documents_per_user = 10_000
default_max_chunks_per_user = 10_000_000
default_max_collections_per_user = 5_000
default_max_upload_size = 214748364800  # 200GB

# 模型配置
fast_llm = "openai/gpt-4o-mini"      # 内部操作用
quality_llm = "openai/gpt-4o"        # 用户输出用
vlm = "openai/gpt-4o"                # 视觉输入用
audio_lm = "openai/whisper-1"        # 转录用
reasoning_llm = "openai/o1-mini"     # 推理智能体用
planning_llm = "anthropic/claude-3-5-sonnet-20241022"  # 规划智能体用

[agent]
rag_tools = ["search_file_descriptions", "search_file_knowledge", "get_file_content"]
research_tools = ["rag", "reasoning", "critique", "python_executor"]
```

## 核心功能

### 1. 文档管理

#### 文档上传
```python
# 从文件上传
result = client.documents.create(file_path="/path/to/document.pdf")

# 从原始文本上传
result = client.documents.create(
    raw_text="This is some text content",
    metadata={"title": "My Document", "author": "John Doe"}
)

# 从预处理的文本块上传
result = client.documents.create(
    chunks=["Chunk 1 content", "Chunk 2 content"],
    metadata={"source": "manual_input"}
)
```

#### 文档列表和管理
```python
# 列出所有文档
documents = client.documents.list()

# 获取特定文档
document = client.documents.retrieve(document_id="doc-uuid")

# 更新文档
client.documents.update(
    document_id="doc-uuid",
    metadata={"updated": True}
)

# 删除文档
client.documents.delete(document_id="doc-uuid")
```

### 2. 搜索功能

#### 基础搜索
```python
# 向量搜索
results = client.retrieval.search(
    query="What is machine learning?",
    vector_search_settings={
        "use_vector_search": True,
        "search_limit": 10,
        "filters": {"document_type": "pdf"}
    }
)
```

#### 混合搜索
```python
# 结合向量搜索和全文搜索
results = client.retrieval.search(
    query="artificial intelligence applications",
    vector_search_settings={
        "use_vector_search": True,
        "use_hybrid_search": True,
        "search_limit": 20
    }
)
```

### 3. RAG (检索增强生成)

#### 基础 RAG
```python
response = client.retrieval.rag(
    query="Explain the benefits of renewable energy",
    rag_generation_config={
        "model": "openai/gpt-4o",
        "temperature": 0.7,
        "max_tokens_to_sample": 1000,
        "stream": False
    }
)

print(response.results[0].completion)
```

#### 流式 RAG
```python
response = client.retrieval.rag(
    query="What are the latest developments in AI?",
    rag_generation_config={
        "model": "openai/gpt-4o",
        "stream": True
    }
)

for chunk in response:
    if hasattr(chunk, 'completion'):
        print(chunk.completion, end='', flush=True)
```

### 4. 智能体对话

#### 基础智能体交互
```python
response = client.retrieval.agent(
    message={
        "role": "user",
        "content": "Analyze the market implications of renewable energy adoption"
    },
    rag_generation_config={
        "model": "anthropic/claude-3-5-sonnet-20241022",
        "temperature": 0.8
    }
)
```

#### 多轮对话
```python
messages = [
    {"role": "system", "content": "You are a helpful AI assistant."},
    {"role": "user", "content": "What is quantum computing?"},
    {"role": "assistant", "content": "Quantum computing is..."},
    {"role": "user", "content": "How does it differ from classical computing?"}
]

response = client.retrieval.agent(
    messages=messages,
    rag_generation_config={
        "model": "openai/gpt-4o",
        "extended_thinking": True,
        "thinking_budget": 4096
    }
)
```

## API 使用指南

### 认证

R2R 支持基于令牌的认证：

```python
# 登录获取令牌
client.users.login(
    email="<EMAIL>",
    password="change_me_immediately"
)

# 或直接设置令牌
client = R2RClient(
    base_url="http://localhost:7272",
    access_token="your-access-token"
)
```

### 集合管理

集合用于组织和管理文档：

```python
# 创建集合
collection = client.collections.create(
    name="Research Papers",
    description="Collection of AI research papers"
)

# 将文档添加到集合
client.collections.add_document(
    collection_id=collection.results.id,
    document_id="doc-uuid"
)

# 在集合中搜索
results = client.retrieval.search(
    query="machine learning",
    vector_search_settings={
        "collection_ids": [collection.results.id]
    }
)
```

### 知识图谱

R2R 支持自动构建和查询知识图谱：

```python
# 从文档提取实体和关系
client.documents.extract(document_id="doc-uuid")

# 构建知识图谱
client.graphs.build(collection_id="collection-uuid")

# 查询知识图谱
graph_results = client.retrieval.search(
    query="Show me relationships between AI and healthcare",
    graph_settings={
        "use_graph_search": True,
        "graph_search_settings": {
            "search_limit": 10
        }
    }
)
```

## 部署方式

### Docker 部署

#### 轻量模式
```bash
# 拉取镜像
docker pull sciphiai/r2r:latest

# 运行容器
docker run -d \
  --name r2r \
  -p 7272:7272 \
  -e OPENAI_API_KEY=sk-your-key \
  sciphiai/r2r:latest
```

#### 完整模式
```bash
# 使用 Docker Compose
cd R2R
docker compose -f docker/compose.full.yaml up -d
```

### Kubernetes 部署

R2R 提供了 Kubernetes 部署配置：

```bash
# 应用 Kubernetes 配置
kubectl apply -f deployment/k8s/
```

### 云平台部署

#### Google Cloud Platform
```bash
# 创建 Compute Engine 实例
gcloud compute instances create r2r-instance \
  --image-family=ubuntu-2004-lts \
  --image-project=ubuntu-os-cloud \
  --machine-type=e2-standard-4

# SSH 连接并安装
gcloud compute ssh r2r-instance
sudo apt update && sudo apt install python3-pip docker.io -y
pip install r2r
```

#### AWS EC2
```bash
# 启动 EC2 实例后
sudo yum update -y
sudo yum install python3-pip docker -y
sudo service docker start
pip3 install r2r
```

## 高级功能

### 1. 自定义工具

创建自定义工具扩展智能体功能：

```python
# 在 docker/user_tools/ 目录下创建工具
from core.base.agent.tools.base import Tool

class CustomTool(Tool):
    def __init__(self):
        super().__init__(
            name="custom_tool",
            description="A custom tool for specific tasks",
            parameters={
                "type": "object",
                "properties": {
                    "input": {
                        "type": "string",
                        "description": "Input parameter"
                    }
                },
                "required": ["input"]
            },
            results_function=self.execute
        )
    
    def execute(self, input: str):
        # 实现自定义逻辑
        return f"Processed: {input}"
```

### 2. 高级 RAG 技术

#### HyDE (假设文档嵌入)
```python
response = client.retrieval.rag(
    query="What are the applications of quantum computing?",
    vector_search_settings={
        "use_hyde": True,
        "hyde_generation_config": {
            "model": "openai/gpt-4o-mini",
            "temperature": 0.7
        }
    }
)
```

#### RAG-Fusion
```python
response = client.retrieval.rag(
    query="Climate change impacts",
    vector_search_settings={
        "use_rag_fusion": True,
        "rag_fusion_config": {
            "num_queries": 3,
            "rerank_limit": 20
        }
    }
)
```

### 3. 网络搜索集成

```python
# 启用网络搜索的 RAG
response = client.retrieval.rag(
    query="Latest developments in AI 2024",
    include_web_search=True,
    rag_generation_config={
        "model": "openai/gpt-4o"
    }
)

# 智能体使用网络搜索工具
response = client.retrieval.agent(
    message={
        "role": "user",
        "content": "Research the current state of renewable energy adoption globally"
    },
    rag_tools=["web_search", "web_scrape", "search_file_knowledge"]
)
```

## 故障排除

### 常见问题

#### 1. 连接问题
```bash
# 检查服务状态
curl -f http://localhost:7272/v3/health

# 查看日志
docker logs r2r
```

#### 2. 内存不足
```bash
# 增加 Docker 内存限制
docker run -m 8g sciphiai/r2r:latest

# 或在 docker-compose.yml 中设置
services:
  r2r:
    mem_limit: 8g
```

#### 3. API 密钥问题
```bash
# 验证环境变量
echo $OPENAI_API_KEY

# 在容器中检查
docker exec r2r env | grep OPENAI_API_KEY
```

#### 4. 数据库连接问题
```bash
# 检查 PostgreSQL 状态
docker exec postgres pg_isready -U postgres

# 重置数据库
docker compose down -v
docker compose up -d
```

### 性能优化

#### 1. 数据库优化
```sql
-- 增加连接池大小
ALTER SYSTEM SET max_connections = 1024;
ALTER SYSTEM SET shared_buffers = '256MB';
```

#### 2. 向量搜索优化
```python
# 调整搜索参数
vector_search_settings = {
    "search_limit": 50,  # 减少搜索结果数量
    "use_hybrid_search": False,  # 禁用混合搜索以提高速度
    "selected_collection_ids": ["specific-collection"]  # 限制搜索范围
}
```

#### 3. 缓存配置
```bash
# 启用 Redis 缓存
export R2R_REDIS_URL=redis://localhost:6379
```

### 监控和日志

#### 1. 启用详细日志
```bash
export R2R_LOG_LEVEL=DEBUG
export R2R_LOG_FORMAT=json
```

#### 2. Sentry 集成
```bash
export R2R_SENTRY_DSN=your-sentry-dsn
export R2R_SENTRY_ENVIRONMENT=production
export R2R_SENTRY_TRACES_SAMPLE_RATE=0.1
```

#### 3. 健康检查
```python
# 检查系统状态
health = client.system.health()
print(health)

# 获取系统信息
info = client.system.info()
print(info)
```

## 社区和支持

- **官方文档**: https://r2r-docs.sciphi.ai/
- **GitHub**: https://github.com/SciPhi-AI/R2R
- **Discord**: https://discord.gg/p6KqD2kjtB
- **问题报告**: https://github.com/SciPhi-AI/R2R/issues

## 许可证

R2R 使用 MIT 许可证。详见 [LICENSE.md](LICENSE.md) 文件。

---

*本文档基于 R2R v3.6.5 版本编写，如有更新请参考官方文档。*
